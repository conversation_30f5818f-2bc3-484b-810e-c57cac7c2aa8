# URL复制功能使用说明

## 🎯 功能概述

您的博客现在已经集成了完整的 **URL复制功能**，读者可以轻松复制文章中的任何链接。

## ✨ 功能特点

### 1. 自动识别多种URL格式
- **普通URL**：`https://example.com`
- **Markdown链接**：`[文本](URL)`
- **箭头格式**：`文本 → URL`
- **代码块中的URL**：自动识别并添加复制按钮

### 2. 智能复制按钮
- **📋** - 普通文本和链接的复制按钮
- **🔗** - 代码块中URL的复制按钮
- **悬停效果** - 按钮颜色变化和缩放
- **复制提示** - 成功/失败的视觉反馈

### 3. 用户体验优化
- 一键复制URL地址
- 美观的动画效果
- 防重复添加按钮
- 错误处理机制

## 🚀 使用方法

### 创建新文章
```bash
hexo new "文章标题"
```
新文章将自动使用包含URL复制功能的模板。

### 启动博客
```bash
# 方法1：直接启动（推荐）
hexo server
# 或简写
hexo s

# 方法2：使用批处理脚本
./restart-blog.bat

# 方法3：使用PowerShell脚本
./blog-restart.ps1

# 方法4：手动启动
hexo clean
hexo generate
hexo server
```

### 解决启动异常
如果执行 `hexo clean` 后出现启动异常，请使用以下方法：

1. **使用批处理脚本**（推荐）：
   ```bash
   ./restart-blog.bat
   ```

2. **使用PowerShell脚本**：
   ```powershell
   ./blog-restart.ps1
   ```

3. **手动修复**：
   ```bash
   hexo clean
   npm install
   hexo generate
   hexo server --port 4001
   ```

## 📝 文章模板说明

新的文章模板 (`scaffolds/post.md`) 包含：
- 完整的文章结构
- 各种URL格式示例
- 分类和标签字段
- 封面图片字段

## 🔧 技术实现

### 修改的文件
1. **`themes/cola/layout/_partial/article.ejs`** - 主要功能实现
2. **`themes/cola/source/css/partial/article.css`** - 样式定义
3. **`scaffolds/post.md`** - 文章模板

### 核心功能
- JavaScript自动识别URL
- 动态添加复制按钮
- Clipboard API复制功能
- CSS动画和样式

## 🎨 样式自定义

如需自定义按钮样式，请编辑：
```css
/* 文件：themes/cola/source/css/partial/article.css */

/* 普通复制按钮 */
.copy-url-btn {
  /* 自定义样式 */
}

/* 代码块复制按钮 */
.copy-code-url-btn {
  /* 自定义样式 */
}
```

## 🐛 常见问题

### Q: 执行 `hexo clean` 后博客无法启动
**A**: 使用 `./restart-blog.bat` 脚本或手动执行：
```bash
hexo clean
npm install
hexo generate
hexo server
```

### Q: 复制按钮不显示
**A**: 检查JavaScript是否正常加载，确保文章中包含有效的URL。

### Q: 端口被占用
**A**: 使用不同端口启动：
```bash
hexo server --port 4001
```

### Q: URL复制功能不工作
**A**: 确保浏览器支持Clipboard API，或检查控制台是否有JavaScript错误。

## 📚 支持的URL格式示例

### 1. 普通URL
```
访问 https://example.com 了解更多
```

### 2. 箭头格式
```
官网 → https://example.com
GitHub → https://github.com/user/repo
```

### 3. Markdown格式
```
[官方网站](https://example.com)
[GitHub仓库](https://github.com/user/repo)
```

### 4. 代码块
```bash
curl https://api.example.com/data
git clone https://github.com/user/repo.git
```

## 🎉 总结

您的博客现在具备了完整的URL复制功能！读者可以轻松复制任何链接，大大提升了用户体验。

如有任何问题，请检查本文档的常见问题部分或使用提供的优化脚本。
