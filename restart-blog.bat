@echo off
chcp 65001 >nul
echo.
echo 🚀 博客重启优化脚本
echo ==================
echo.

echo 📁 清理缓存...
call hexo clean

echo.
echo 📦 检查依赖...
call npm install

echo.
echo 🔨 重新生成博客...
call hexo generate

echo.
if exist "public\index.html" (
    echo ✅ 博客生成成功!
    echo.
    echo 🌐 启动本地服务器...
    echo 📝 博客将在 http://localhost:4000 启动
    echo 🔗 URL复制功能已启用
    echo ⚠️  按 Ctrl+C 停止服务器
    echo.
    call hexo server
) else (
    echo ❌ 博客生成失败，请检查配置文件
    pause
)
