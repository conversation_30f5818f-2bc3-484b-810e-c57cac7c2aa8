# Hexo 博客重启优化脚本
# 解决 hexo clean 后启动异常的问题

Write-Host "🚀 开始优化博客启动..." -ForegroundColor Green

# 1. 清理缓存和生成文件
Write-Host "📁 清理缓存和生成文件..." -ForegroundColor Yellow
hexo clean

# 2. 检查依赖
Write-Host "📦 检查依赖..." -ForegroundColor Yellow
npm install

# 3. 重新生成博客
Write-Host "🔨 重新生成博客..." -ForegroundColor Yellow
hexo generate

# 4. 检查生成结果
if (Test-Path "public/index.html") {
    Write-Host "✅ 博客生成成功!" -ForegroundColor Green
    
    # 5. 启动服务器
    Write-Host "🌐 启动本地服务器..." -ForegroundColor Yellow
    Write-Host "📝 博客将在 http://localhost:4000 启动" -ForegroundColor Cyan
    Write-Host "🔗 URL复制功能已启用，点击文章中的 📋 或 🔗 按钮即可复制链接" -ForegroundColor Cyan
    Write-Host "⚠️  如果端口4000被占用，请手动使用: hexo server --port 4001" -ForegroundColor Yellow
    
    hexo server
} else {
    Write-Host "❌ 博客生成失败，请检查配置文件" -ForegroundColor Red
    Write-Host "🔍 建议检查以下文件:" -ForegroundColor Yellow
    Write-Host "   - _config.yml" -ForegroundColor White
    Write-Host "   - themes/cola/_config.yml" -ForegroundColor White
    Write-Host "   - source/_posts/ 目录下的文章文件" -ForegroundColor White
}
