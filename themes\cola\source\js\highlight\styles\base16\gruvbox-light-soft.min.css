/*!
  Theme: Gruvbox light, soft
  Author: <PERSON><PERSON><PERSON> (<EMAIL>), mor<PERSON><PERSON> (https://github.com/morhetz/gruvbox)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#504945;background:#f2e5bc}.hljs ::selection,.hljs::selection{background-color:#d5c4a1;color:#504945}.hljs-comment{color:#bdae93}.hljs-tag{color:#665c54}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#504945}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#9d0006}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#af3a03}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#b57614}.hljs-strong{font-weight:700;color:#b57614}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#79740e}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#427b58}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#076678}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#8f3f71}.hljs-emphasis{color:#8f3f71;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#d65d0e}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}