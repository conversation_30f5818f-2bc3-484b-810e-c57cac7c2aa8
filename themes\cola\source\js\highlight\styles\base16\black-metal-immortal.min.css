/*!
  Theme: Black Metal (Immortal)
  Author: metalelf0 (https://github.com/metalelf0)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#c1c1c1;background:#000}.hljs ::selection,.hljs::selection{background-color:#222;color:#c1c1c1}.hljs-comment{color:#333}.hljs-tag{color:#999}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#c1c1c1}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#5f8787}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#aaa}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#567}.hljs-strong{font-weight:700;color:#567}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#79b}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#aaa}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#888}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#999}.hljs-emphasis{color:#999;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#444}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}