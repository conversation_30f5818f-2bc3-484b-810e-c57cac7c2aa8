/*!
  Theme: Humanoid dark
  Author: <PERSON> (ta<PERSON>) Friese
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#f8f8f2;background:#232629}.hljs ::selection,.hljs::selection{background-color:#484e54;color:#f8f8f2}.hljs-comment{color:#60615d}.hljs-tag{color:#c0c0bd}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#f8f8f2}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#f11235}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#ff9505}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#ffb627}.hljs-strong{font-weight:700;color:#ffb627}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#02d849}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#0dd9d6}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#00a6fb}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#f15ee3}.hljs-emphasis{color:#f15ee3;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#b27701}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}