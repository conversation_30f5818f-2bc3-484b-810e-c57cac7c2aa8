{"name": "@highlightjs/cdn-assets", "description": "Syntax highlighting with language autodetection. (pre-compiled CDN assets)", "keywords": ["highlight", "syntax"], "homepage": "https://highlightjs.org/", "version": "11.8.0", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <****************>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "bugs": {"url": "https://github.com/highlightjs/highlight.js/issues"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git://github.com/highlightjs/highlight.js.git"}, "sideEffects": ["*.css", "*.scss"], "scripts": {"mocha": "mocha", "lint": "eslint src/*.js src/lib/*.js demo/*.js tools/**/*.js --ignore-pattern vendor", "lint-languages": "eslint --no-eslintrc -c .eslintrc.lang.js src/languages/**/*.js", "build_and_test": "npm run build && npm run test", "build_and_test_browser": "npm run build-browser && npm run test-browser", "build": "node ./tools/build.js -t node", "build-cdn": "node ./tools/build.js -t cdn", "build-browser": "node ./tools/build.js -t browser :common", "devtool": "npx http-server", "test": "mocha test", "test-markup": "mocha test/markup", "test-detect": "mocha test/detect", "test-browser": "mocha test/browser", "test-parser": "mocha test/parser"}, "engines": {"node": ">=12.0.0"}, "devDependencies": {"@colors/colors": "^1.5.0", "@rollup/plugin-commonjs": "^24.1.0", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^15.0.2", "@types/mocha": "^10.0.1", "@typescript-eslint/eslint-plugin": "^5.59.1", "@typescript-eslint/parser": "^5.59.1", "clean-css": "^5.3.2", "cli-table": "^0.3.1", "commander": "^10.0.1", "css": "^3.0.0", "css-color-names": "^1.0.1", "deep-freeze-es6": "^2.0.0", "del": "^6.1.1", "dependency-resolver": "^2.0.1", "eslint": "^8.39.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "glob": "^8.0.3", "glob-promise": "^6.0.2", "handlebars": "^4.7.6", "http-server": "^14.1.1", "jsdom": "^21.1.1", "lodash": "^4.17.20", "mocha": "^10.2.0", "refa": "^0.4.1", "rollup": "^2.47.0", "should": "^13.2.3", "terser": "^5.17.1", "tiny-worker": "^2.3.0", "typescript": "^5.0.4", "wcag-contrast": "^3.0.0"}}